<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  
  <appSettings>
	  <add key="ApiBaseUrl" value="http://localhost:82" />

	  <!-- PPT插件角色权限配置 -->
	  <!-- PPT插件基础用户角色ID - ppt-电芯检测插件角色 -->
	  <add key="PptUserRoleId" value="1947464193174003713" />

	  <!-- 电芯检测功能角色ID - 使用相同的PPT插件角色 -->
	  <add key="BatteryDetectionRoleId" value="1947464193174003713" />

	  <!-- 管理员角色ID - 拥有所有权限 -->
	  <add key="AdminRoleId" value="9876543210" />
  </appSettings>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
