using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using Microsoft.Office.Interop.PowerPoint;
using PBIppt.ApiClient;
using PBIppt.Models;
using PBIppt.Utils;
using PowerPoint = Microsoft.Office.Interop.PowerPoint;
using Office = Microsoft.Office.Core;

namespace PBIppt.ImageProcessing
{
    /// <summary>
    /// PowerPoint内置裁剪功能助手
    /// 直接调用PowerPoint的裁剪API，用户可以继续手动调整
    /// </summary>
    public static class PowerPointCropHelper
    {
        /// <summary>
        /// 确保图片Scale为标准值 - 关键修复
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        private static void EnsureStandardScale(PowerPoint.Shape shape)
        {
            try
            {
                // 解锁宽高比，允许独立调整宽度和高度
                shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                // 重置Scale为100%标准值
                shape.ScaleHeight(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
                shape.ScaleWidth(1.0f, Microsoft.Office.Core.MsoTriState.msoTrue);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                // Scale标准化失败时继续执行，不影响主要功能
            }
        }



        /// <summary>
        /// 根据电芯检测结果直接设置PowerPoint图片的裁剪属性（指定图片尺寸，默认使用直接转换）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        public static void ApplyDetectionResultToCrop(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            int actualPixelWidth, int actualPixelHeight)
        {
            // 默认使用直接转换方法
            ApplyDetectionResultToCrop(shape, detectionResult, actualPixelWidth, actualPixelHeight, true);
        }

        /// <summary>
        /// 根据电芯检测结果设置PowerPoint图片的裁剪属性（指定图片尺寸和转换方法）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="detectionResult">电芯检测结果</param>
        /// <param name="actualPixelWidth">实际图片像素宽度</param>
        /// <param name="actualPixelHeight">实际图片像素高度</param>
        /// <param name="useDirectConversion">是否使用直接转换方法（true=直接按原始尺寸，false=考虑PPT显示缩放）</param>
        public static void ApplyDetectionResultToCrop(PowerPoint.Shape shape, BatteryDetectionApiResult detectionResult,
            int actualPixelWidth, int actualPixelHeight, bool useDirectConversion)
        {
            try
            {
                // 验证形状类型
                if (shape.Type != Office.MsoShapeType.msoPicture)
                {
                    throw new ArgumentException("选中的形状不是图片");
                }

                // 关键修复：确保Scale为标准值
                EnsureStandardScale(shape);
                var currentWidth = shape.Width;
                var currentHeight = shape.Height;

                Debug.WriteLine("currentWidth" + currentWidth + "currentHeight" +currentHeight);
                // 只使用百分比坐标转换
                CropCoordinates cropCoords = null;
                bool hasValidPercentageCoords = HasValidPercentageCoordinates(detectionResult);

                if (hasValidPercentageCoords)
                {
                    cropCoords = ConvertPercentageCoordsToPptCrop(
                        detectionResult.CropLeftPercent,
                        detectionResult.CropTopPercent,
                        detectionResult.CropRightPercent,
                        detectionResult.CropBottomPercent,
                        currentWidth,
                        currentHeight);
                }
                else
                {
                    // 如果没有有效的百分比坐标，抛出异常
                    throw new ArgumentException("没有有效的百分比坐标，无法进行裁剪");
                }



                // 应用裁剪到PowerPoint图片
                if (cropCoords != null)
                {
                    //ApplyCropToShape(shape, cropCoords);
                    ApplyCropToShapeByPoint(shape, detectionResult.CropLeft, detectionResult.CropTop, detectionResult.CropRight, detectionResult.CropBottom, detectionResult.CropLeftPercent, detectionResult.CropTopPercent, detectionResult.CropRightPercent, detectionResult.CropBottomPercent);
                }

                // 裁剪完成后自动调整图片尺寸为2.1cm高度
                AdjustImageSizeTo21cm(shape);

                // 选中图片展示结果
                SelectShapeToShowResult(shape);
            }
            catch (Exception ex)
            {
                Logger.Exception(ex);
                throw new Exception($"应用检测结果失败: {ex.Message}", ex);
            }
        }

        // 已删除 ConvertApiCoordsToPptCrop_Direct 方法，只保留百分比坐标转换



        /// <summary>
        /// 检查API返回的百分比坐标是否有效
        /// </summary>
        /// <param name="detectionResult">检测结果</param>
        /// <returns>如果百分比坐标有效返回true，否则返回false</returns>
        private static bool HasValidPercentageCoordinates(BatteryDetectionApiResult detectionResult)
        {
            // 检查百分比坐标是否都大于0（API返回0表示无效或未提供）
            bool hasValidCoords = detectionResult.CropLeftPercent > 0 ||
                                 detectionResult.CropTopPercent > 0 ||
                                 detectionResult.CropRightPercent > 0 ||
                                 detectionResult.CropBottomPercent > 0;

            return hasValidCoords;
        }

        /// <summary>
        /// 将API返回的百分比坐标转换为PowerPoint裁剪坐标
        /// </summary>
        /// <param name="leftPercent">左边裁剪百分比</param>
        /// <param name="topPercent">上边裁剪百分比</param>
        /// <param name="rightPercent">右边裁剪百分比</param>
        /// <param name="bottomPercent">下边裁剪百分比</param>
        /// <param name="shapeWidth">PowerPoint形状宽度</param>
        /// <param name="shapeHeight">PowerPoint形状高度</param>
        /// <returns>PowerPoint裁剪坐标</returns>
        private static CropCoordinates ConvertPercentageCoordsToPptCrop(
            float leftPercent, float topPercent, float rightPercent, float bottomPercent,
            float shapeWidth, float shapeHeight)
        {
            // 直接将百分比转换为PowerPoint的裁剪points
            // API返回的百分比表示从各边裁剪的比例
            var cropCoords = new CropCoordinates
            {
                Left = leftPercent * shapeWidth,
                Top = topPercent * shapeHeight,
                Right = rightPercent * shapeWidth,
                Bottom = bottomPercent * shapeHeight
            };

            return cropCoords;
        }



        /// <summary>
        /// 直接使用百分比设置PowerPoint Shape的裁剪属性
        /// 避免任何像素到points的转换，直接基于图片的百分比位置进行裁剪
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        private static void ApplyPercentageCropToShape(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始百分比裁剪 ===");
                Debug.WriteLine($"输入百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 获取图片的原始尺寸（PowerPoint中的显示尺寸）
                float shapeWidth = shape.Width;
                float shapeHeight = shape.Height;
                Debug.WriteLine($"PowerPoint形状尺寸: {shapeWidth:F2}x{shapeHeight:F2} points");

                // 直接将百分比转换为PowerPoint的裁剪points
                float cropLeft = leftPercent * shapeWidth;
                float cropTop = topPercent * shapeHeight;
                float cropRight = rightPercent * shapeWidth;
                float cropBottom = bottomPercent * shapeHeight;

                Debug.WriteLine($"计算的裁剪points: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 百分比裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"百分比裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 直接使用API返回的百分比坐标进行裁剪（适用于被拉伸的图片）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        private static void ApplyApiPercentageCropToShape(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始API百分比坐标裁剪 ===");
                Debug.WriteLine($"API百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");

                // 重新分析：基于左边图片成功，右边图片失败的情况
                // API百分比坐标应该是边界框位置，需要转换为裁剪距离
                Debug.WriteLine("使用边界框位置转换为裁剪距离的逻辑");

                float cropLeft = leftPercent * 100;              // 从左边裁剪的百分比 = 边界框左边位置
                float cropTop = topPercent * 100;                // 从上边裁剪的百分比 = 边界框上边位置
                float cropRight = (1 -rightPercent) * 100;   // 从右边裁剪的百分比 = 100% - 边界框右边位置
                float cropBottom = (1 - bottomPercent) * 100; // 从下边裁剪的百分比 = 100% - 边界框下边位置

                // 验证保留区域大小
                float preservedWidthPercent = (rightPercent - leftPercent) * 100;
                float preservedHeightPercent = (bottomPercent - topPercent) * 100;
                Debug.WriteLine($"保留区域百分比: 宽度={preservedWidthPercent:F2}%, 高度={preservedHeightPercent:F2}%");

                Debug.WriteLine($"转换为PowerPoint裁剪百分比: Left={cropLeft:F2}%, Top={cropTop:F2}%, Right={cropRight:F2}%, Bottom={cropBottom:F2}%");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ API百分比坐标裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"API百分比坐标裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"API百分比坐标裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 使用百分比坐标进行裁剪，基于原始图片尺寸（适用于被拉伸的图片）
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        /// <param name="originalPixelWidth">原始图片像素宽度</param>
        /// <param name="originalPixelHeight">原始图片像素高度</param>
        private static void ApplyPercentageCropToShapeWithOriginalSize(PowerPoint.Shape shape, float leftPercent, float topPercent, float rightPercent, float bottomPercent, int originalPixelWidth, int originalPixelHeight)
        {
            try
            {
                Debug.WriteLine("=== 开始基于原始尺寸的百分比裁剪 ===");
                Debug.WriteLine($"输入百分比坐标: Left={leftPercent:F6}, Top={topPercent:F6}, Right={rightPercent:F6}, Bottom={bottomPercent:F6}");
                Debug.WriteLine($"原始图片像素尺寸: {originalPixelWidth}x{originalPixelHeight}");

                // 根据Microsoft官方文档：裁剪是相对于图片的原始尺寸计算的
                // API百分比坐标是位置，需要转换为PowerPoint的裁剪距离
                float cropLeft = leftPercent * originalPixelWidth;  // 从左边裁剪的距离
                float cropTop = topPercent * originalPixelHeight;   // 从上边裁剪的距离
                float cropRight = (1.0f - rightPercent) * originalPixelWidth;   // 从右边裁剪的距离
                float cropBottom = (1.0f - bottomPercent) * originalPixelHeight; // 从下边裁剪的距离

                Debug.WriteLine($"基于原始尺寸计算的裁剪points: Left={cropLeft:F2}, Top={cropTop:F2}, Right={cropRight:F2}, Bottom={cropBottom:F2}");

                // 确保图片不锁定宽高比，允许自由裁剪
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }

                // 应用裁剪
                shape.PictureFormat.CropLeft = cropLeft;
                shape.PictureFormat.CropTop = cropTop;
                shape.PictureFormat.CropRight = cropRight;
                shape.PictureFormat.CropBottom = cropBottom;

                Debug.WriteLine("✓ 基于原始尺寸的百分比裁剪应用完成");

                // 验证裁剪后的尺寸
                var finalWidth = shape.Width;
                var finalHeight = shape.Height;
                Debug.WriteLine($"裁剪后形状尺寸: {finalWidth:F2}x{finalHeight:F2} points");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"基于原始尺寸的百分比裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"基于原始尺寸的百分比裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 通过像素和百分比计算裁剪的point值，然后应用到PowerPoint形状
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="leftPixels">左边裁剪像素值</param>
        /// <param name="topPixels">上边裁剪像素值</param>
        /// <param name="rightPixels">右边裁剪像素值</param>
        /// <param name="bottomPixels">下边裁剪像素值</param>
        /// <param name="leftPercent">左边裁剪百分比 (0.0-1.0)</param>
        /// <param name="topPercent">上边裁剪百分比 (0.0-1.0)</param>
        /// <param name="rightPercent">右边裁剪百分比 (0.0-1.0)</param>
        /// <param name="bottomPercent">下边裁剪百分比 (0.0-1.0)</param>
        public static void ApplyCropToShapeByPoint(PowerPoint.Shape shape, 
            float leftPixels, float topPixels, float rightPixels, float bottomPixels,
            float leftPercent, float topPercent, float rightPercent, float bottomPercent)
        {
            try
            {
                Debug.WriteLine("=== 开始通过Point和像素计算裁剪 ===");
                
                // 获取图片的原始尺寸（像素）
                var (originalPixelWidth, originalPixelHeight) = GetImagePixelDimensions(shape);
                Debug.WriteLine($"图片原始像素尺寸: {originalPixelWidth}x{originalPixelHeight}");
                
                // 获取当前PowerPoint形状尺寸（points）
                float currentWidthPoints = shape.Width;
                float currentHeightPoints = shape.Height;
                Debug.WriteLine($"当前形状尺寸: {currentWidthPoints:F2}x{currentHeightPoints:F2} points");
                
                // 计算像素到points的转换比例
                float pixelsToPointsX = currentWidthPoints / originalPixelWidth;
                float pixelsToPointsY = currentHeightPoints / originalPixelHeight;
                Debug.WriteLine($"像素到points转换比例: X={pixelsToPointsX:F4}, Y={pixelsToPointsY:F4}");
                
                // 方法1：基于像素值计算裁剪points
                float cropLeftByPixels = leftPixels * pixelsToPointsX;
                float cropTopByPixels = topPixels * pixelsToPointsY;
                float cropRightByPixels = rightPixels * pixelsToPointsX;
                float cropBottomByPixels = bottomPixels * pixelsToPointsY;
                
                // 方法2：基于百分比计算裁剪points
                float cropLeftByPercent = leftPercent * currentWidthPoints;
                float cropTopByPercent = topPercent * currentHeightPoints;
                float cropRightByPercent = (1.0f - rightPercent) * currentWidthPoints;
                float cropBottomByPercent = (1.0f - bottomPercent) * currentHeightPoints;
                
                Debug.WriteLine("=== 裁剪计算结果 ===");
                Debug.WriteLine($"基于像素计算的裁剪points: Left={cropLeftByPixels:F2}, Top={cropTopByPixels:F2}, Right={cropRightByPixels:F2}, Bottom={cropBottomByPixels:F2}");
                Debug.WriteLine($"基于百分比计算的裁剪points: Left={cropLeftByPercent:F2}, Top={cropTopByPercent:F2}, Right={cropRightByPercent:F2}, Bottom={cropBottomByPercent:F2}");
                
                // 使用百分比计算的结果（更精确）
                float finalCropLeft = cropLeftByPercent;
                float finalCropTop = cropTopByPercent;
                float finalCropRight = cropRightByPercent;
                float finalCropBottom = cropBottomByPercent;
                
                // 确保图片不锁定宽高比
                try
                {
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;
                    Debug.WriteLine("✓ 已解锁图片宽高比");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"解锁宽高比失败: {ex.Message}");
                }
                
                // 应用裁剪
                shape.PictureFormat.CropLeft = finalCropLeft;
                shape.PictureFormat.CropTop = finalCropTop;
                shape.PictureFormat.CropRight = finalCropRight;
                shape.PictureFormat.CropBottom = finalCropBottom;
                
                Debug.WriteLine("✓ 裁剪已应用");
                
                // 验证裁剪结果
                float finalWidth = shape.Width;
                float finalHeight = shape.Height;
                float actualCropWidth = currentWidthPoints - finalCropLeft - finalCropRight;
                float actualCropHeight = currentHeightPoints - finalCropTop - finalCropBottom;
                
                Debug.WriteLine("=== 裁剪验证结果 ===");
                Debug.WriteLine($"裁剪前尺寸: {currentWidthPoints:F2}x{currentHeightPoints:F2} points");
                Debug.WriteLine($"裁剪后尺寸: {finalWidth:F2}x{finalHeight:F2} points");
                Debug.WriteLine($"实际裁剪区域: {actualCropWidth:F2}x{actualCropHeight:F2} points");
                
                // 计算预期裁剪区域
                float expectedCropWidth = (rightPercent - leftPercent) * currentWidthPoints;
                float expectedCropHeight = (bottomPercent - topPercent) * currentHeightPoints;
                
                Debug.WriteLine($"预期裁剪区域: {expectedCropWidth:F2}x{expectedCropHeight:F2} points");
                
                // 计算误差
                float widthError = Math.Abs(actualCropWidth - expectedCropWidth);
                float heightError = Math.Abs(actualCropHeight - expectedCropHeight);
                
                Debug.WriteLine($"裁剪误差: 宽度误差={widthError:F2} points, 高度误差={heightError:F2} points");
                
                // 安全的界面刷新
                try
                {
                    var app = shape.Application;
                    if (app?.ActiveWindow != null)
                    {
                        var activeWindow = app.ActiveWindow;
                        if (activeWindow?.View?.Slide != null)
                        {
                            var currentSlideIndex = activeWindow.View.Slide.SlideIndex;
                            activeWindow.View.GotoSlide(currentSlideIndex);
                            Debug.WriteLine("已刷新PowerPoint界面");
                        }
                    }
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"界面刷新失败: {refreshEx.Message}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"通过Point和像素裁剪失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"通过Point和像素裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 直接设置PowerPoint Shape的裁剪属性
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <param name="cropCoords">裁剪坐标</param>
        private static void ApplyCropToShape(PowerPoint.Shape shape, CropCoordinates cropCoords)
        {
            try
            {
                Debug.WriteLine("开始应用裁剪到PowerPoint形状");

                shape.PictureFormat.CropLeft = cropCoords.Left;
                shape.PictureFormat.CropTop = cropCoords.Top;
                shape.PictureFormat.CropRight = cropCoords.Right;
                shape.PictureFormat.CropBottom = cropCoords.Bottom;

                // 安全的界面刷新方式 - 优化版本，避免不必要的异常
                try
                {
                    var app = shape.Application;
                    if (app?.ActiveWindow != null)
                    {
                        // 直接使用PowerPoint支持的刷新方法，避免dynamic调用产生异常
                        try
                        {
                            // 使用GotoSlide方法强制刷新（PowerPoint原生支持）
                            var activeWindow = app.ActiveWindow;
                            if (activeWindow?.View?.Slide != null)
                            {
                                var currentSlideIndex = activeWindow.View.Slide.SlideIndex;
                                activeWindow.View.GotoSlide(currentSlideIndex);
                                Debug.WriteLine("已使用GotoSlide方法刷新PowerPoint界面");
                            }
                            else
                            {
                                Debug.WriteLine("无法获取当前幻灯片，跳过界面刷新");
                            }
                        }
                        catch (Exception refreshEx)
                        {
                            Debug.WriteLine($"PowerPoint界面刷新失败: {refreshEx.Message}");
                            // 界面刷新失败不影响核心功能，继续执行
                        }
                    }
                    else
                    {
                        Debug.WriteLine("无法获取ActiveWindow，跳过界面刷新");
                    }
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"刷新界面失败: {refreshEx.Message}，继续执行");
                    // 不抛出异常，继续执行
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"应用裁剪到形状失败: {ex.Message}");
                Logger.Exception(ex);
                throw new Exception($"应用裁剪失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 调整图片尺寸为2.1cm高度（保持宽高比）并确保图片在幻灯片内
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        public static void AdjustImageSizeTo21cm(PowerPoint.Shape shape)
        {
            try
            {
                Debug.WriteLine("=== 开始调整图片尺寸为2.1cm高度 ===");

                // 目标高度：2.1cm = 59.535点
                float targetHeightCm = 2.1f;
                float targetHeightPoints = targetHeightCm * 28.35f; // 1cm = 28.35点

                Debug.WriteLine($"目标高度: {targetHeightCm}cm = {targetHeightPoints:F2}点");

                // 获取当前图片尺寸和位置
                float currentWidth = shape.Width;
                float currentHeight = shape.Height;
                float currentLeft = shape.Left;
                float currentTop = shape.Top;
                float currentAspectRatio = currentWidth / currentHeight;

                // 计算新的宽度（保持宽高比）
                float newWidth = targetHeightPoints * currentAspectRatio;
                float newHeight = targetHeightPoints;

                // 获取幻灯片尺寸
                var slide = shape.Parent as PowerPoint.Slide;
                if (slide != null)
                {
                    float slideWidth = slide.Master.Width;
                    float slideHeight = slide.Master.Height;
                    Debug.WriteLine($"幻灯片尺寸: {slideWidth:F2}x{slideHeight:F2}点");
                    float currentCenterX = currentLeft + currentWidth / 2;
                    float currentCenterY = currentTop + currentHeight / 2;

                    // 计算新的左上角位置（保持中心点不变）
                    float newLeft = currentCenterX - newWidth / 2;
                    float newTop = currentCenterY - newHeight / 2;

                    // 确保图片不超出幻灯片边界
                    if (newLeft < 0)
                    {
                        newLeft = 10; // 左边距10点
                        Debug.WriteLine("调整Left位置，避免超出左边界");
                    }
                    if (newTop < 0)
                    {
                        newTop = 10; // 上边距10点
                        Debug.WriteLine("调整Top位置，避免超出上边界");
                    }
                    if (newLeft + newWidth > slideWidth)
                    {
                        newLeft = slideWidth - newWidth - 10; // 右边距10点
                        Debug.WriteLine("调整Left位置，避免超出右边界");
                    }
                    if (newTop + newHeight > slideHeight)
                    {
                        newTop = slideHeight - newHeight - 10; // 下边距10点
                        Debug.WriteLine("调整Top位置，避免超出下边界");
                    }

                    Debug.WriteLine($"调整后位置: Left={newLeft:F2}, Top={newTop:F2}");

                    // 解锁宽高比，允许独立调整
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                    // 设置新的尺寸和位置
                    shape.Width = newWidth;
                    shape.Height = newHeight;
                    shape.Left = newLeft;
                    shape.Top = newTop;

                    Debug.WriteLine($"✅ 图片尺寸和位置已调整: 尺寸{newWidth:F2}x{newHeight:F2}点, 位置({newLeft:F2},{newTop:F2})");
                    Debug.WriteLine($"实际高度: {newHeight / 28.35f:F2}cm");
                }
                else
                {
                    Debug.WriteLine("⚠️ 无法获取幻灯片信息，仅调整尺寸");

                    // 解锁宽高比，允许独立调整
                    shape.LockAspectRatio = Microsoft.Office.Core.MsoTriState.msoFalse;

                    // 设置新的尺寸
                    shape.Width = newWidth;
                    shape.Height = newHeight;

                    Debug.WriteLine($"✅ 图片尺寸已调整为2.1cm高度: {newWidth:F2}x{newHeight:F2}点");
                    Debug.WriteLine($"实际高度: {newHeight / 28.35f:F2}cm");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"调整图片尺寸失败: {ex.Message}");
                Logger.Exception(ex);
                // 不抛出异常，避免影响主要功能
            }
        }

        /// <summary>
        /// 选中图片以展示裁剪结果，不进入裁剪模式
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        public static void SelectShapeToShowResult(PowerPoint.Shape shape)
        {
            try
            {
                try
                {
                    var app = shape.Application;
                    var activeWindow = app.ActiveWindow;
                    var selection = activeWindow.Selection;
                    selection.Unselect();
                    System.Threading.Thread.Sleep(50); // 短暂延迟确保取消选择生效
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"取消选择失败: {ex.Message}");
                }

                // 选中图片以展示裁剪结果
                shape.Select();
                Debug.WriteLine("已选中图片，展示裁剪结果");

                // 强制刷新PowerPoint界面以确保裁剪效果可见
                try
                {
                    var app = shape.Application;
                    var activeWindow = app.ActiveWindow;
                    var view = activeWindow.View;
                    view.Zoom = view.Zoom; // 触发重绘
                    Debug.WriteLine("已触发PowerPoint界面刷新");
                }
                catch (Exception refreshEx)
                {
                    Debug.WriteLine($"刷新界面失败: {refreshEx.Message}");
                }

            }
            catch (Exception ex)
            {
                Debug.WriteLine($"选中图片展示结果失败: {ex.Message}");
                Logger.Exception(ex);
                // 这个错误不是致命的，不抛出异常
            }
        }




        /// <summary>
        /// 获取图片的原始像素尺寸
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>图片的像素宽度和高度</returns>
        private static (int width, int height) GetImagePixelDimensions(PowerPoint.Shape shape)
        {
            try
            {


                // 方法1：尝试使用ImageExtractor提取图片
                try
                {
                    var image = ImageExtractor.ExtractImageFromShape(shape);
                    if (image != null)
                    {
                        int width = image.Width;
                        int height = image.Height;

                        Debug.WriteLine($"通过ImageExtractor获取到图片原始像素尺寸: {width}x{height}");

                        // 释放图片资源
                        image.Dispose();

                        return (width, height);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"ImageExtractor方法失败: {ex.Message}");
                }

                // 方法2：尝试通过PowerPoint的PictureFormat属性获取
                try
                {
                    // 获取图片的原始尺寸（以points为单位）
                    var originalWidth = shape.PictureFormat.CropLeft + shape.Width + shape.PictureFormat.CropRight;
                    var originalHeight = shape.PictureFormat.CropTop + shape.Height + shape.PictureFormat.CropBottom;

                    Debug.WriteLine($"通过PictureFormat计算的原始尺寸: {originalWidth:F2}x{originalHeight:F2} points");

                    // 假设72 DPI (1 point = 1 pixel at 72 DPI)
                    // 这是一个近似值，但比完全错误的默认值要好
                    int pixelWidth = (int)Math.Round(originalWidth);
                    int pixelHeight = (int)Math.Round(originalHeight);

                    Debug.WriteLine($"转换为像素尺寸: {pixelWidth}x{pixelHeight}");

                    if (pixelWidth > 0 && pixelHeight > 0)
                    {
                        return (pixelWidth, pixelHeight);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"PictureFormat方法失败: {ex.Message}");
                }

                // 方法3：基于当前显示尺寸进行估算
                try
                {
                    // 假设图片在PowerPoint中没有被大幅缩放
                    // 使用当前显示尺寸作为近似的像素尺寸
                    int estimatedWidth = (int)Math.Round(shape.Width);
                    int estimatedHeight = (int)Math.Round(shape.Height);

                    Debug.WriteLine($"基于显示尺寸估算的像素尺寸: {estimatedWidth}x{estimatedHeight}");

                    if (estimatedWidth > 0 && estimatedHeight > 0)
                    {
                        return (estimatedWidth, estimatedHeight);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"尺寸估算失败: {ex.Message}");
                }

                // 最后的备选方案：使用合理的默认尺寸
                Debug.WriteLine("所有方法都失败，使用默认图片尺寸: 1920x1080");
                return (1920, 1080);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取图片像素尺寸完全失败: {ex.Message}");
                Logger.Exception(ex);

                // 发生错误时使用默认尺寸
                Debug.WriteLine("使用默认图片尺寸: 1920x1080");
                return (1920, 1080);
            }
        }

        /// <summary>
        /// 验证裁剪是否已应用
        /// </summary>
        /// <param name="shape">PowerPoint图片形状</param>
        /// <returns>裁剪状态信息</returns>
        public static string VerifyCropApplication(PowerPoint.Shape shape)
        {
            try
            {
                if (shape.Type != Office.MsoShapeType.msoPicture)
                {
                    return "选中的形状不是图片";
                }

                var cropLeft = shape.PictureFormat.CropLeft;
                var cropTop = shape.PictureFormat.CropTop;
                var cropRight = shape.PictureFormat.CropRight;
                var cropBottom = shape.PictureFormat.CropBottom;

                var status = $"当前裁剪属性 - Left: {cropLeft:F2}, Top: {cropTop:F2}, Right: {cropRight:F2}, Bottom: {cropBottom:F2}";
                Debug.WriteLine(status);

                bool hasCropping = cropLeft > 0 || cropTop > 0 || cropRight > 0 || cropBottom > 0;
                if (hasCropping)
                {
                    return $"✓ 裁剪已应用 - {status}";
                }
                else
                {
                    return $"✗ 未检测到裁剪 - {status}";
                }
            }
            catch (Exception ex)
            {
                var error = $"验证裁剪状态失败: {ex.Message}";
                Debug.WriteLine(error);
                Logger.Exception(ex);
                return error;
            }
        }






    }

    /// <summary>
    /// 裁剪坐标结构
    /// </summary>
    public class CropCoordinates
    {
        /// <summary>
        /// 左边裁剪距离（points）
        /// </summary>
        public float Left { get; set; }

        /// <summary>
        /// 上边裁剪距离（points）
        /// </summary>
        public float Top { get; set; }

        /// <summary>
        /// 右边裁剪距离（points）
        /// </summary>
        public float Right { get; set; }

        /// <summary>
        /// 下边裁剪距离（points）
        /// </summary>
        public float Bottom { get; set; }
    }
}
