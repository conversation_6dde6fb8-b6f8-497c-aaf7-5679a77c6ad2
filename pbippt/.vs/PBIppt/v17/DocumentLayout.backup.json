{"Version": 1, "WorkspaceRootPath": "D:\\gitlocal\\pbi\\pbippt\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|d:\\gitlocal\\pbi\\pbippt\\pbippt\\imageprocessing\\powerpointcrophelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|solutionrelative:pbippt\\imageprocessing\\powerpointcrophelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|D:\\gitlocal\\pbi\\pbippt\\pbippt\\imageprocessing\\batterydetectionprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|solutionrelative:pbippt\\imageprocessing\\batterydetectionprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|D:\\gitlocal\\pbi\\pbippt\\pbippt\\models\\batterydetectionresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|solutionrelative:pbippt\\models\\batterydetectionresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|D:\\gitlocal\\pbi\\pbippt\\pbippt\\models\\authmodels.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{817897C7-9F90-4F5C-93DC-BC1A0097D946}|PBIppt\\PBIppt.csproj|solutionrelative:pbippt\\models\\authmodels.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "PowerPointCropHelper.cs", "DocumentMoniker": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\ImageProcessing\\PowerPointCropHelper.cs", "RelativeDocumentMoniker": "PBIppt\\ImageProcessing\\PowerPointCropHelper.cs", "ToolTip": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\ImageProcessing\\PowerPointCropHelper.cs", "RelativeToolTip": "PBIppt\\ImageProcessing\\PowerPointCropHelper.cs", "ViewState": "AgIAACoAAAAAAAAAAAAewDIAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T05:34:47.284Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "BatteryDetectionProcessor.cs", "DocumentMoniker": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\ImageProcessing\\BatteryDetectionProcessor.cs", "RelativeDocumentMoniker": "PBIppt\\ImageProcessing\\BatteryDetectionProcessor.cs", "ToolTip": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\ImageProcessing\\BatteryDetectionProcessor.cs", "RelativeToolTip": "PBIppt\\ImageProcessing\\BatteryDetectionProcessor.cs", "ViewState": "AgIAAG8AAAAAAAAAAAAawIoAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T05:03:04.687Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "BatteryDetectionResult.cs", "DocumentMoniker": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\Models\\BatteryDetectionResult.cs", "RelativeDocumentMoniker": "PBIppt\\Models\\BatteryDetectionResult.cs", "ToolTip": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\Models\\BatteryDetectionResult.cs", "RelativeToolTip": "PBIppt\\Models\\BatteryDetectionResult.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T05:02:56.896Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "AuthModels.cs", "DocumentMoniker": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\Models\\AuthModels.cs", "RelativeDocumentMoniker": "PBIppt\\Models\\AuthModels.cs", "ToolTip": "D:\\gitlocal\\pbi\\pbippt\\PBIppt\\Models\\AuthModels.cs", "RelativeToolTip": "PBIppt\\Models\\AuthModels.cs", "ViewState": "AgIAAHoBAAAAAAAAAAAAwFMBAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T01:50:29.534Z"}]}]}]}